<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Physics System Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            background: white; 
            padding: 25px; 
            border-radius: 10px; 
            max-width: 800px; 
            margin: 0 auto; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
        }
        h1 { 
            text-align: center; 
            color: #2c3e50; 
            margin-bottom: 10px; 
        }
        .subtitle { 
            text-align: center; 
            color: #7f8c8d; 
            margin-bottom: 30px; 
            font-size: 16px; 
        }
        .coin-container { 
            display: flex; 
            justify-content: center; 
            margin: 25px 0; 
            padding: 25px; 
            background: linear-gradient(135deg, #2c3e50, #34495e); 
            border-radius: 10px; 
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.3); 
        }
        .coin-canvas { 
            border: 2px solid #ecf0f1; 
            border-radius: 8px; 
            box-shadow: 0 4px 8px rgba(0,0,0,0.2); 
        }
        .controls { 
            text-align: center; 
            margin: 25px 0; 
        }
        .btn { 
            padding: 15px 30px; 
            margin: 10px; 
            border: none; 
            border-radius: 8px; 
            background: linear-gradient(135deg, #3498db, #2980b9); 
            color: white; 
            cursor: pointer; 
            font-size: 16px; 
            font-weight: 600; 
            transition: all 0.3s ease; 
            box-shadow: 0 4px 8px rgba(0,0,0,0.2); 
        }
        .btn:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 6px 12px rgba(0,0,0,0.3); 
        }
        .btn:disabled { 
            background: #bdc3c7; 
            cursor: not-allowed; 
            transform: none; 
            box-shadow: none; 
        }
        .btn.heads { 
            background: linear-gradient(135deg, #f39c12, #e67e22); 
        }
        .btn.tails { 
            background: linear-gradient(135deg, #9b59b6, #8e44ad); 
        }
        .debug { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 8px; 
            margin: 20px 0; 
            font-family: 'Courier New', monospace; 
            font-size: 12px; 
            white-space: pre-wrap; 
            max-height: 400px; 
            overflow-y: auto; 
            border: 1px solid #dee2e6; 
        }
        .physics-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #27ae60;
        }
        .physics-info h3 {
            margin-top: 0;
            color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 New Physics System Test</h1>
        <p class="subtitle">ทดสอบระบบ Physics แบบใหม่: ใช้ความสูงและความเร็วหมุนตอนขึ้น, ใช้แรงโน้มถ่วงตอนลงและเด้ง</p>
        
        <div class="physics-info">
            <h3>🔬 ระบบ Physics ใหม่ (ไม่ใช้ Gravity)</h3>
            <p><strong>ช่วงขึ้น (Ascending):</strong> ใช้ความสูงสูงสุด (maxFlipHeight) และความเร็วหมุน (flipRotationSpeed)</p>
            <p><strong>ช่วงลง (Descending):</strong> ใช้ความสูงที่กำหนดแบบ smooth descent (ไม่ใช้ gravity)</p>
            <p><strong>ช่วงเด้ง (Bouncing):</strong> ใช้ความสูงที่กำหนดและความเร็วหมุนที่ช้าลง (bounceRotationSpeed)</p>
        </div>
        
        <div class="coin-container">
            <canvas id="coinCanvas" width="400" height="400" class="coin-canvas"></canvas>
        </div>

        <div class="controls">
            <button id="headsBtn" class="btn heads">👑 ทดสอบหัว</button>
            <button id="tailsBtn" class="btn tails">🦅 ทดสอบก้อย</button>
            <button id="resetBtn" class="btn">🔄 รีเซ็ต</button>
        </div>

        <div class="debug" id="debug">กำลังโหลด...</div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="../src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper = null;
        let debugElement = document.getElementById('debug');

        function log(message) {
            console.log(message);
            debugElement.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            debugElement.scrollTop = debugElement.scrollHeight;
        }

        async function init() {
            try {
                log('🚀 เริ่มต้นระบบ Physics แบบใหม่...');
                
                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.03,
                    flipDuration: 3000, // เพิ่มเวลาเพื่อดูการเปลี่ยนแปลงชัดเจน
                    enableSound: true
                });

                await coinFlipper.ready();
                log('✅ CoinFlipper พร้อมใช้งาน!');
                
                // แสดงค่าการตั้งค่า Physics ใหม่
                const renderer = coinFlipper.coinRenderer;
                log(`📊 การตั้งค่า Physics (ไม่ใช้ Gravity):`);
                log(`   - ความสูงสูงสุด: ${renderer.maxFlipHeight}`);
                log(`   - ความเร็วหมุนขึ้น: ${renderer.flipRotationSpeed} rad/frame`);
                log(`   - ความเร็วหมุนเด้ง: ${renderer.bounceRotationSpeed} rad/frame`);
                log(`   - Bounce Damping: ${renderer.bounceDamping}`);
                log(`   - ระยะเวลาโยน: ${renderer.options.flipDuration}ms`);

                await coinFlipper.startIdle();
                log('🔄 เริ่ม idle animation');

                setupEventListeners();

            } catch (error) {
                log('❌ ข้อผิดพลาด: ' + error.message);
                console.error(error);
            }
        }

        function setupEventListeners() {
            document.getElementById('headsBtn').onclick = () => testNewPhysics('heads');
            document.getElementById('tailsBtn').onclick = () => testNewPhysics('tails');
            document.getElementById('resetBtn').onclick = () => resetToIdle();
        }

        async function testNewPhysics(result) {
            try {
                log(`\n🎯 === ทดสอบ Physics ใหม่: ${result} ===`);
                
                // ปิดปุ่มระหว่างทดสอบ
                document.querySelectorAll('.btn').forEach(btn => btn.disabled = true);

                await coinFlipper.stopIdle();
                log('⏹️ หยุด idle animation');

                // เริ่มการทอยและติดตามสถานะ
                log('🚀 เริ่มการทอย...');
                const startTime = Date.now();
                
                // ติดตามสถานะ Physics ทุก 100ms
                const tracker = setInterval(() => {
                    const renderer = coinFlipper.coinRenderer;
                    if (renderer && renderer.isFlipping) {
                        const elapsed = Date.now() - startTime;
                        log(`📈 [${elapsed}ms] Phase: ${renderer.flipPhase}, Y: ${renderer.coinPositionY.toFixed(3)}, VelY: ${renderer.velocity.y.toFixed(3)}`);
                    }
                }, 100);

                const flipResult = await coinFlipper.toss(result);
                clearInterval(tracker);
                
                const totalTime = Date.now() - startTime;
                log(`✅ การทอยเสร็จสิ้น! ผลลัพธ์: ${flipResult} (ใช้เวลา ${totalTime}ms)`);
                
                // แสดงสถานะสุดท้าย
                const renderer = coinFlipper.coinRenderer;
                log(`📊 สถานะสุดท้าย:`);
                log(`   - Phase: ${renderer.flipPhase}`);
                log(`   - Position Y: ${renderer.coinPositionY.toFixed(3)}`);
                log(`   - Rotation X: ${(renderer.coinRotationX * 180 / Math.PI).toFixed(1)}°`);
                log(`   - Bounce Count: ${renderer.bounceCount}/${renderer.targetBounces}`);

            } catch (error) {
                log('❌ ข้อผิดพลาดในการทดสอบ: ' + error.message);
                console.error(error);
            } finally {
                // เปิดปุ่มกลับ
                document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
            }
        }

        async function resetToIdle() {
            try {
                log('\n🔄 รีเซ็ตกลับสู่สถานะ Idle...');
                await coinFlipper.startIdle();
                log('✅ รีเซ็ตเสร็จสิ้น');
            } catch (error) {
                log('❌ ข้อผิดพลาดในการรีเซ็ต: ' + error.message);
            }
        }

        window.addEventListener('load', init);
    </script>
</body>
</html>
